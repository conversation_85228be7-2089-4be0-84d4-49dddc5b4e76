import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon

# Configurar la figura
fig, ax = plt.subplots(figsize=(10, 8))

# Definir colores
color_azul = '#0096B4'  # Azul para trapecio OPQR
color_morado = '#783C96'  # Morado para trapecio EFGH

# === COORDENADAS DE LOS TRAPECIOS ===

# Trapecio superior OPQR (azul)
O = np.array([1, 2])
P = np.array([2.5, 1])
Q = np.array([4, 2.5])
R = np.array([2.5, 3.5])

# Trapecio inferior EFGH (morado) - reflejado
E = np.array([6, -1])
F = np.array([4.5, 0])
G = np.array([5.5, 1])
H = np.array([7, 0])

# === DIBUJAR LA LÍNEA DE REFLEXIÓN K ===
# Línea k con pendiente aproximada
x_linea = np.linspace(-1, 9, 100)
y_linea = 0.75 * x_linea - 0.5  # Pendiente 3/4 aproximadamente
ax.plot(x_linea, y_linea, 'k-', linewidth=2, label='Eje k')

# === DIBUJAR LOS TRAPECIOS ===

# Trapecio OPQR (azul)
trapecio_azul = Polygon([O, P, Q, R], facecolor=color_azul, alpha=0.7, edgecolor='black', linewidth=2)
ax.add_patch(trapecio_azul)

# Trapecio EFGH (morado)
trapecio_morado = Polygon([E, F, G, H], facecolor=color_morado, alpha=0.7, edgecolor='black', linewidth=2)
ax.add_patch(trapecio_morado)

# === ETIQUETAS DE LOS VÉRTICES ===

# Trapecio OPQR
ax.text(O[0]-0.2, O[1], 'O', fontsize=14, fontweight='bold', ha='right')
ax.text(P[0], P[1]-0.3, 'P', fontsize=14, fontweight='bold', ha='center')
ax.text(Q[0]+0.1, Q[1], 'Q', fontsize=14, fontweight='bold', ha='left')
ax.text(R[0]-0.1, R[1]+0.2, 'R', fontsize=14, fontweight='bold', ha='center')

# Trapecio EFGH
ax.text(E[0], E[1]-0.3, 'E', fontsize=14, fontweight='bold', ha='center')
ax.text(F[0]-0.2, F[1], 'F', fontsize=14, fontweight='bold', ha='right')
ax.text(G[0], G[1]+0.2, 'G', fontsize=14, fontweight='bold', ha='center')
ax.text(H[0]+0.1, H[1], 'H', fontsize=14, fontweight='bold', ha='left')

# === ETIQUETA DEL EJE K ===
ax.text(7.5, 5, 'k', fontsize=16, fontweight='bold')

# === TEXTO EXPLICATIVO ===
ax.text(8, 2, 'El eje k es paralelo\na los lados OR, PQ, FG y EH', 
        fontsize=12, fontweight='bold', ha='left', va='center',
        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

# === PUNTOS EN LOS VÉRTICES ===
vertices = [O, P, Q, R, E, F, G, H]
for vertice in vertices:
    ax.plot(vertice[0], vertice[1], 'ko', markersize=6)

# === CONFIGURACIÓN DE LOS EJES ===
ax.set_xlim(-1, 10)
ax.set_ylim(-2, 6)
ax.set_aspect('equal')
ax.grid(True, alpha=0.3)
ax.set_xlabel('X', fontsize=12)
ax.set_ylabel('Y', fontsize=12)

# Ocultar los números de los ejes para que se vea más limpio
ax.set_xticks([])
ax.set_yticks([])

# === TÍTULO ===
ax.set_title('Reflexión de Trapecios respecto al Eje k', fontsize=14, fontweight='bold', pad=20)

plt.tight_layout()
plt.show()

# === FUNCIÓN PARA GUARDAR LA FIGURA ===
def guardar_figura(nombre_archivo='trapecios_reflexion.png'):
    """
    Guarda la figura en un archivo
    """
    plt.savefig(nombre_archivo, dpi=300, bbox_inches='tight')
    print(f"Figura guardada como: {nombre_archivo}")

# === FUNCIÓN PARA MOSTRAR INFORMACIÓN DE LOS TRAPECIOS ===
def mostrar_info_trapecios():
    """
    Muestra información sobre las coordenadas y propiedades de los trapecios
    """
    print("=== INFORMACIÓN DE LOS TRAPECIOS ===")
    print(f"Trapecio OPQR (azul):")
    print(f"  O: {O}")
    print(f"  P: {P}")
    print(f"  Q: {Q}")
    print(f"  R: {R}")
    print()
    print(f"Trapecio EFGH (morado):")
    print(f"  E: {E}")
    print(f"  F: {F}")
    print(f"  G: {G}")
    print(f"  H: {H}")
    print()
    print("La línea k tiene pendiente 3/4 y actúa como eje de reflexión")

# Ejecutar funciones adicionales si se ejecuta directamente
if __name__ == "__main__":
    mostrar_info_trapecios()
    # Descomentar la siguiente línea para guardar la figura
    # guardar_figura()
