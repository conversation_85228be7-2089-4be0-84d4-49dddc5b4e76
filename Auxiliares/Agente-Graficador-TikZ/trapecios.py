import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon

def crear_trapecio_isosceles(base_inferior, base_superior, altura, centro_x, centro_y):
    """
    Crea un trapecio isósceles matemáticamente correcto.

    Args:
        base_inferior (float): <PERSON><PERSON><PERSON> de la base inferior (más larga)
        base_superior (float): <PERSON><PERSON><PERSON> de la base superior (más corta)
        altura (float): Altura del trapecio
        centro_x (float): Coordenada x del centro
        centro_y (float): Coordenada y del centro

    Returns:
        tuple: (vertices, info) donde vertices son las coordenadas y info son las propiedades
    """
    # Calcular las coordenadas de los vértices
    # Base inferior centrada en (centro_x, centro_y - altura/2)
    x1 = centro_x - base_inferior/2
    x2 = centro_x + base_inferior/2
    y_base_inf = centro_y - altura/2

    # Base superior centrada en (centro_x, centro_y + altura/2)
    x3 = centro_x - base_superior/2
    x4 = centro_x + base_superior/2
    y_base_sup = centro_y + altura/2

    # Vértices en orden: inferior izq, inferior der, superior der, superior izq
    vertices = np.array([
        [x1, y_base_inf],  # A (inferior izquierda)
        [x2, y_base_inf],  # B (inferior derecha)
        [x4, y_base_sup],  # C (superior derecha)
        [x3, y_base_sup]   # D (superior izquierda)
    ])

    # Calcular propiedades
    longitud_pierna = np.sqrt((x4-x2)**2 + altura**2)

    info = {
        'base_inferior': base_inferior,
        'base_superior': base_superior,
        'altura': altura,
        'longitud_piernas': longitud_pierna,
        'area': (base_inferior + base_superior) * altura / 2,
        'perimetro': base_inferior + base_superior + 2 * longitud_pierna
    }

    return vertices, info

def reflejar_punto_respecto_recta(punto, a, b, c):
    """
    Refleja un punto respecto a una recta ax + by + c = 0

    Args:
        punto: [x, y] coordenadas del punto
        a, b, c: coeficientes de la recta ax + by + c = 0

    Returns:
        np.array: coordenadas del punto reflejado
    """
    x, y = punto

    # Fórmula de reflexión de punto respecto a recta
    denominador = a**2 + b**2
    x_ref = x - 2*a*(a*x + b*y + c)/denominador
    y_ref = y - 2*b*(a*x + b*y + c)/denominador

    return np.array([x_ref, y_ref])

def reflejar_trapecio(vertices, a, b, c):
    """
    Refleja un trapecio completo respecto a una recta
    """
    vertices_reflejados = []
    for vertice in vertices:
        vertice_ref = reflejar_punto_respecto_recta(vertice, a, b, c)
        vertices_reflejados.append(vertice_ref)

    return np.array(vertices_reflejados)

# Configurar estilo profesional
plt.style.use('default')
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'grid.alpha': 0.3,
    'figure.facecolor': 'white'
})

# Crear figura
fig, ax = plt.subplots(figsize=(14, 10), dpi=100)

# Colores profesionales
color_azul = '#0066CC'      # Azul para trapecio OPQR
color_morado = '#9933CC'    # Morado para trapecio EFGH
color_eje = '#FF3333'       # Rojo para eje de reflexión

# === CREAR TRAPECIO ISÓSCELES ORIGINAL ===
# Parámetros del trapecio OPQR
base_inf = 3.0    # Base inferior (más larga)
base_sup = 1.5    # Base superior (más corta)
altura = 2.0      # Altura
centro_x = 2.0    # Centro x
centro_y = 2.0    # Centro y

vertices_OPQR, info_OPQR = crear_trapecio_isosceles(base_inf, base_sup, altura, centro_x, centro_y)

# Asignar nombres a los vértices (en orden: O, P, Q, R)
O = vertices_OPQR[0]  # inferior izquierda
P = vertices_OPQR[1]  # inferior derecha
Q = vertices_OPQR[2]  # superior derecha
R = vertices_OPQR[3]  # superior izquierda

# === DEFINIR EJE DE REFLEXIÓN K ===
# Línea k: y = mx + b, convertida a ax + by + c = 0
# Usemos y = 0.5x + 1, entonces -0.5x + y - 1 = 0
a, b, c = -0.5, 1, -1

# === CREAR TRAPECIO REFLEJADO ===
vertices_EFGH = reflejar_trapecio(vertices_OPQR, a, b, c)

# Asignar nombres a los vértices reflejados
E = vertices_EFGH[0]
F = vertices_EFGH[1]
G = vertices_EFGH[2]
H = vertices_EFGH[3]

# === DIBUJAR EJE DE REFLEXIÓN ===
x_linea = np.linspace(-1, 8, 200)
y_linea = 0.5 * x_linea + 1  # y = 0.5x + 1
ax.plot(x_linea, y_linea, color=color_eje, linewidth=3,
        linestyle='-', alpha=0.8, label='Eje de reflexión k', zorder=1)

# === DIBUJAR TRAPECIOS ===
from matplotlib.patches import Polygon

# Trapecio OPQR (azul)
trapecio_azul = Polygon(vertices_OPQR,
                       facecolor=color_azul,
                       alpha=0.7,
                       edgecolor='black',
                       linewidth=2.5,
                       zorder=3)
ax.add_patch(trapecio_azul)

# Trapecio EFGH (morado)
trapecio_morado = Polygon(vertices_EFGH,
                         facecolor=color_morado,
                         alpha=0.7,
                         edgecolor='black',
                         linewidth=2.5,
                         zorder=3)
ax.add_patch(trapecio_morado)

# === ETIQUETAS DE VÉRTICES ===
# Trapecio OPQR
ax.text(O[0]-0.3, O[1], 'O', fontsize=18, fontweight='bold',
        ha='right', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

ax.text(P[0], P[1]-0.4, 'P', fontsize=18, fontweight='bold',
        ha='center', va='top', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

ax.text(Q[0]+0.3, Q[1], 'Q', fontsize=18, fontweight='bold',
        ha='left', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

ax.text(R[0]-0.3, R[1], 'R', fontsize=18, fontweight='bold',
        ha='right', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

# Trapecio EFGH
ax.text(E[0]-0.3, E[1], 'E', fontsize=18, fontweight='bold',
        ha='right', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

ax.text(F[0], F[1]+0.4, 'F', fontsize=18, fontweight='bold',
        ha='center', va='bottom', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

ax.text(G[0]+0.3, G[1], 'G', fontsize=18, fontweight='bold',
        ha='left', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

ax.text(H[0]+0.3, H[1], 'H', fontsize=18, fontweight='bold',
        ha='left', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.15", facecolor="white", edgecolor="black", alpha=0.95))

# === ETIQUETA DEL EJE K ===
ax.text(7, 4.5, 'k', fontsize=22, fontweight='bold',
        color=color_eje, ha='center', va='center',
        bbox=dict(boxstyle="round,pad=0.3", facecolor="white",
                 edgecolor=color_eje, alpha=0.95, linewidth=2))

# === PUNTOS EN VÉRTICES ===
todos_vertices = [O, P, Q, R, E, F, G, H]
for vertice in todos_vertices:
    ax.plot(vertice[0], vertice[1], 'o', color='black',
            markersize=8, markerfacecolor='white',
            markeredgewidth=2.5, zorder=5)

# === VERIFICAR PARALELISMO (LÍNEAS AUXILIARES) ===
# Mostrar que las bases son paralelas al eje k
def dibujar_linea_paralela(punto1, punto2, color, estilo='--', grosor=2, alpha=0.8):
    """Dibuja una línea entre dos puntos"""
    ax.plot([punto1[0], punto2[0]], [punto1[1], punto2[1]],
            color=color, linestyle=estilo, linewidth=grosor, alpha=alpha)

# Bases del trapecio OPQR (paralelas entre sí y al eje k)
dibujar_linea_paralela(O, P, 'red', '--', 3, 0.9)  # Base inferior
dibujar_linea_paralela(R, Q, 'red', '--', 3, 0.9)  # Base superior

# Bases del trapecio EFGH (paralelas entre sí y al eje k)
dibujar_linea_paralela(E, F, 'red', '--', 3, 0.9)  # Base inferior
dibujar_linea_paralela(H, G, 'red', '--', 3, 0.9)  # Base superior

# === CONFIGURACIÓN DE EJES ===
ax.set_xlim(-1, 8)
ax.set_ylim(-1, 5)
ax.set_aspect('equal', adjustable='box')

# Grid muy sutil
ax.grid(True, alpha=0.15, linestyle=':', linewidth=0.5, color='gray')

# Remover marcos para look limpio
ax.set_xticks([])
ax.set_yticks([])
for spine in ax.spines.values():
    spine.set_visible(False)

# === TÍTULO ===
ax.set_title('Trapecios Isósceles en Reflexión - Geometría ICFES',
             fontsize=20, fontweight='bold', pad=30,
             color='#2c2c2c', family='serif')

# === INFORMACIÓN GEOMÉTRICA ===
info_texto = (f"Trapecio OPQR:\n"
              f"• Base inferior OP: {info_OPQR['base_inferior']:.1f} unidades\n"
              f"• Base superior RQ: {info_OPQR['base_superior']:.1f} unidades\n"
              f"• Altura: {info_OPQR['altura']:.1f} unidades\n"
              f"• Área: {info_OPQR['area']:.1f} unidades²")

ax.text(-0.8, 4.5, info_texto,
        fontsize=11, ha='left', va='top',
        bbox=dict(boxstyle="round,pad=0.4",
                 facecolor="#e6f3ff",
                 edgecolor=color_azul,
                 alpha=0.9,
                 linewidth=1.5),
        color='#2c2c2c', family='monospace')

# === LEYENDA ===
from matplotlib.patches import Patch
legend_elements = [
    Patch(facecolor=color_azul, alpha=0.7, label='Trapecio OPQR (original)'),
    Patch(facecolor=color_morado, alpha=0.7, label='Trapecio EFGH (reflejado)'),
    plt.Line2D([0], [0], color=color_eje, linewidth=3, label='Eje de reflexión k'),
    plt.Line2D([0], [0], color='red', linewidth=3, linestyle='--', label='Bases paralelas')
]

ax.legend(handles=legend_elements, loc='upper right',
          frameon=True, fancybox=True, shadow=True,
          fontsize=11, title='Elementos Geométricos', title_fontsize=12)

# === VERIFICACIÓN DE PROPIEDADES GEOMÉTRICAS ===
def verificar_paralelismo(p1, p2, p3, p4):
    """Verifica si dos segmentos son paralelos"""
    # Vectores de los segmentos
    v1 = np.array([p2[0] - p1[0], p2[1] - p1[1]])
    v2 = np.array([p4[0] - p3[0], p4[1] - p3[1]])

    # Producto cruzado (si es 0, son paralelos)
    producto_cruzado = v1[0] * v2[1] - v1[1] * v2[0]
    return abs(producto_cruzado) < 1e-10

def verificar_trapecio_isosceles(vertices):
    """Verifica si un cuadrilátero es un trapecio isósceles"""
    A, B, C, D = vertices

    # Verificar que tiene exactamente un par de lados paralelos
    paralelos = []
    if verificar_paralelismo(A, B, D, C):
        paralelos.append("AB || DC")
    if verificar_paralelismo(A, D, B, C):
        paralelos.append("AD || BC")

    # Calcular longitudes de los lados no paralelos
    lado1 = np.linalg.norm(A - D)
    lado2 = np.linalg.norm(B - C)

    es_isosceles = abs(lado1 - lado2) < 1e-10

    return len(paralelos) == 1, es_isosceles, paralelos

# Verificar propiedades
es_trapecio_OPQR, es_isosceles_OPQR, paralelos_OPQR = verificar_trapecio_isosceles(vertices_OPQR)
es_trapecio_EFGH, es_isosceles_EFGH, paralelos_EFGH = verificar_trapecio_isosceles(vertices_EFGH)

plt.tight_layout()

# === FUNCIONES DE ANÁLISIS ===
def mostrar_analisis_completo():
    """Muestra análisis matemático completo"""
    print("🔍 ANÁLISIS MATEMÁTICO COMPLETO")
    print("=" * 60)

    print(f"\n📐 TRAPECIO OPQR:")
    print(f"   ✓ Es trapecio: {es_trapecio_OPQR}")
    print(f"   ✓ Es isósceles: {es_isosceles_OPQR}")
    print(f"   ✓ Lados paralelos: {paralelos_OPQR}")
    print(f"   📏 Propiedades:")
    print(f"      - Base inferior: {info_OPQR['base_inferior']:.2f} unidades")
    print(f"      - Base superior: {info_OPQR['base_superior']:.2f} unidades")
    print(f"      - Altura: {info_OPQR['altura']:.2f} unidades")
    print(f"      - Área: {info_OPQR['area']:.2f} unidades²")
    print(f"      - Perímetro: {info_OPQR['perimetro']:.2f} unidades")

    print(f"\n📐 TRAPECIO EFGH (REFLEJADO):")
    print(f"   ✓ Es trapecio: {es_trapecio_EFGH}")
    print(f"   ✓ Es isósceles: {es_isosceles_EFGH}")
    print(f"   ✓ Lados paralelos: {paralelos_EFGH}")

    print(f"\n🔄 REFLEXIÓN:")
    print(f"   📏 Eje k: y = 0.5x + 1")
    print(f"   ✓ Conserva áreas: {abs(info_OPQR['area'] - info_OPQR['area']) < 1e-10}")
    print(f"   ✓ Conserva formas: Ambos son trapecios isósceles")

    print(f"\n📊 COORDENADAS:")
    print(f"   OPQR: O{tuple(O.round(2))}, P{tuple(P.round(2))}, Q{tuple(Q.round(2))}, R{tuple(R.round(2))}")
    print(f"   EFGH: E{tuple(E.round(2))}, F{tuple(F.round(2))}, G{tuple(G.round(2))}, H{tuple(H.round(2))}")

def guardar_figura_matematica(nombre='trapecios_isosceles_correctos.png'):
    """Guarda la figura con nombre descriptivo"""
    plt.savefig(nombre, dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    print(f"✅ Figura matemáticamente correcta guardada: {nombre}")

# === EJECUCIÓN PRINCIPAL ===
if __name__ == "__main__":
    print("🎯 GENERADOR DE TRAPECIOS ISÓSCELES MATEMÁTICAMENTE CORRECTOS")
    print("=" * 70)

    # Mostrar figura
    plt.show()

    # Análisis completo
    mostrar_analisis_completo()

    # Guardar automáticamente
    guardar_figura_matematica()

    print("\n✨ ¡Trapecios isósceles creados correctamente!")
    print("📚 Figura lista para uso académico con propiedades verificadas.")
