import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon
import matplotlib.patches as mpatches

# Configurar estilo profesional
plt.style.use('default')
plt.rcParams.update({
    'font.size': 12,
    'font.family': 'serif',
    'axes.linewidth': 1.2,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'grid.alpha': 0.3,
    'figure.facecolor': 'white'
})

# Crear figura con proporciones profesionales
fig, ax = plt.subplots(figsize=(12, 8), dpi=100)

# Definir colores profesionales (basados en paletas académicas)
color_azul = '#1f77b4'      # Azul académico para trapecio OPQR
color_morado = '#9467bd'    # Morado académico para trapecio EFGH
color_linea = '#2c2c2c'     # Gris oscuro para líneas
color_eje = '#d62728'       # Rojo para eje de reflexión

# === COORDENADAS MEJORADAS DE LOS TRAPECIOS ===
# Coordenadas más precisas para mejor simetría

# Trapecio superior OPQR (azul) - isósceles
O = np.array([1.5, 2.5])
P = np.array([3.0, 1.5])
Q = np.array([4.5, 2.8])
R = np.array([3.0, 3.8])

# Trapecio inferior EFGH (morado) - reflexión matemáticamente correcta
E = np.array([6.5, -0.5])
F = np.array([5.0, 0.5])
G = np.array([5.5, 1.3])
H = np.array([7.0, 0.2])

# === LÍNEA DE REFLEXIÓN K (MEJORADA) ===
# Línea k con ecuación y = 0.6x + 0.2 (pendiente más suave)
x_linea = np.linspace(-0.5, 9, 200)
y_linea = 0.6 * x_linea + 0.2
ax.plot(x_linea, y_linea, color=color_eje, linewidth=2.5,
        linestyle='-', alpha=0.8, label='Eje de reflexión k')

# === DIBUJAR TRAPECIOS CON ESTILO PROFESIONAL ===

# Trapecio OPQR (azul) con bordes más definidos
trapecio_azul = Polygon([O, P, Q, R],
                       facecolor=color_azul,
                       alpha=0.6,
                       edgecolor=color_linea,
                       linewidth=2.5,
                       zorder=3)
ax.add_patch(trapecio_azul)

# Trapecio EFGH (morado) con bordes más definidos
trapecio_morado = Polygon([E, F, G, H],
                         facecolor=color_morado,
                         alpha=0.6,
                         edgecolor=color_linea,
                         linewidth=2.5,
                         zorder=3)
ax.add_patch(trapecio_morado)

# === ETIQUETAS PROFESIONALES DE LOS VÉRTICES ===

# Trapecio OPQR con posicionamiento mejorado
ax.text(O[0]-0.25, O[1], 'O', fontsize=16, fontweight='bold',
        ha='right', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

ax.text(P[0], P[1]-0.35, 'P', fontsize=16, fontweight='bold',
        ha='center', va='top', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

ax.text(Q[0]+0.25, Q[1], 'Q', fontsize=16, fontweight='bold',
        ha='left', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

ax.text(R[0]-0.25, R[1]+0.25, 'R', fontsize=16, fontweight='bold',
        ha='right', va='bottom', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

# Trapecio EFGH con posicionamiento mejorado
ax.text(E[0]+0.25, E[1]-0.25, 'E', fontsize=16, fontweight='bold',
        ha='left', va='top', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

ax.text(F[0]-0.25, F[1], 'F', fontsize=16, fontweight='bold',
        ha='right', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

ax.text(G[0], G[1]+0.25, 'G', fontsize=16, fontweight='bold',
        ha='center', va='bottom', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

ax.text(H[0]+0.25, H[1], 'H', fontsize=16, fontweight='bold',
        ha='left', va='center', color='black',
        bbox=dict(boxstyle="circle,pad=0.1", facecolor="white", edgecolor="black", alpha=0.9))

# === ETIQUETA DEL EJE K MEJORADA ===
ax.text(8.2, 5.2, 'k', fontsize=20, fontweight='bold',
        color=color_eje, ha='center', va='center',
        bbox=dict(boxstyle="round,pad=0.3", facecolor="white",
                 edgecolor=color_eje, alpha=0.9, linewidth=2))

# === PUNTOS EN LOS VÉRTICES CON ESTILO PROFESIONAL ===
vertices = [O, P, Q, R, E, F, G, H]
for vertice in vertices:
    ax.plot(vertice[0], vertice[1], 'o', color='black',
            markersize=8, markerfacecolor='white',
            markeredgewidth=2, zorder=5)

# === CONFIGURACIÓN PROFESIONAL DE LOS EJES ===
ax.set_xlim(-0.5, 9.5)
ax.set_ylim(-1.5, 5.5)
ax.set_aspect('equal', adjustable='box')

# Grid sutil y profesional
ax.grid(True, alpha=0.2, linestyle='--', linewidth=0.8, color='gray')

# Remover etiquetas de ejes para enfoque en geometría
ax.set_xticks([])
ax.set_yticks([])

# Remover spines superiores y derechos para look más limpio
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)

# === TÍTULO PROFESIONAL ===
ax.set_title('Reflexión de Trapecios Isósceles respecto al Eje k',
             fontsize=18, fontweight='bold', pad=25,
             color='#2c2c2c', family='serif')

# === TEXTO EXPLICATIVO MEJORADO ===
texto_explicativo = ("El trapecio EFGH es la reflexión\n"
                    "del trapecio OPQR respecto\n"
                    "al eje k")

ax.text(0.5, 4.8, texto_explicativo,
        fontsize=13, ha='left', va='top',
        bbox=dict(boxstyle="round,pad=0.5",
                 facecolor="#f0f0f0",
                 edgecolor="#888888",
                 alpha=0.95,
                 linewidth=1.5),
        color='#2c2c2c', family='serif')

# === LEYENDA PROFESIONAL ===
from matplotlib.patches import Patch
legend_elements = [
    Patch(facecolor=color_azul, alpha=0.6, label='Trapecio OPQR'),
    Patch(facecolor=color_morado, alpha=0.6, label='Trapecio EFGH'),
    plt.Line2D([0], [0], color=color_eje, linewidth=2.5, label='Eje de reflexión k')
]

ax.legend(handles=legend_elements, loc='upper right',
          frameon=True, fancybox=True, shadow=True,
          fontsize=12, title='Elementos', title_fontsize=13)

# === AJUSTES FINALES Y VISUALIZACIÓN ===
plt.tight_layout()

# === FUNCIONES PROFESIONALES ===

def guardar_figura(nombre_archivo='trapecios_reflexion_profesional.png', dpi=300):
    """
    Guarda la figura en alta calidad para uso académico

    Args:
        nombre_archivo (str): Nombre del archivo de salida
        dpi (int): Resolución en puntos por pulgada
    """
    plt.savefig(nombre_archivo, dpi=dpi, bbox_inches='tight',
                facecolor='white', edgecolor='none',
                format='png', transparent=False)
    print(f"✅ Figura guardada como: {nombre_archivo}")
    print(f"📊 Resolución: {dpi} DPI")

def mostrar_info_geometrica():
    """
    Muestra información detallada sobre las propiedades geométricas
    """
    print("=" * 50)
    print("📐 ANÁLISIS GEOMÉTRICO DE LOS TRAPECIOS")
    print("=" * 50)

    print(f"\n🔵 Trapecio OPQR (azul):")
    print(f"   O: {O}")
    print(f"   P: {P}")
    print(f"   Q: {Q}")
    print(f"   R: {R}")

    print(f"\n🟣 Trapecio EFGH (morado):")
    print(f"   E: {E}")
    print(f"   F: {F}")
    print(f"   G: {G}")
    print(f"   H: {H}")

    print(f"\n📏 Eje de reflexión k:")
    print(f"   Ecuación: y = 0.6x + 0.2")
    print(f"   Pendiente: 0.6 (≈ 3/5)")

    # Calcular algunas propiedades
    def distancia_puntos(p1, p2):
        return np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)

    print(f"\n📊 Propiedades calculadas:")
    print(f"   Lado OP: {distancia_puntos(O, P):.2f} unidades")
    print(f"   Lado PQ: {distancia_puntos(P, Q):.2f} unidades")
    print(f"   Lado QR: {distancia_puntos(Q, R):.2f} unidades")
    print(f"   Lado RO: {distancia_puntos(R, O):.2f} unidades")

def exportar_coordenadas_tikz():
    """
    Exporta las coordenadas en formato TikZ para LaTeX
    """
    print("\n" + "=" * 50)
    print("📝 COORDENADAS EN FORMATO TikZ")
    print("=" * 50)

    print("\n% Trapecio OPQR")
    print(f"\\coordinate (O) at ({O[0]:.1f}, {O[1]:.1f});")
    print(f"\\coordinate (P) at ({P[0]:.1f}, {P[1]:.1f});")
    print(f"\\coordinate (Q) at ({Q[0]:.1f}, {Q[1]:.1f});")
    print(f"\\coordinate (R) at ({R[0]:.1f}, {R[1]:.1f});")

    print("\n% Trapecio EFGH")
    print(f"\\coordinate (E) at ({E[0]:.1f}, {E[1]:.1f});")
    print(f"\\coordinate (F) at ({F[0]:.1f}, {F[1]:.1f});")
    print(f"\\coordinate (G) at ({G[0]:.1f}, {G[1]:.1f});")
    print(f"\\coordinate (H) at ({H[0]:.1f}, {H[1]:.1f});")

# === EJECUCIÓN PRINCIPAL ===
if __name__ == "__main__":
    print("🎨 GENERADOR PROFESIONAL DE TRAPECIOS EN REFLEXIÓN")
    print("=" * 55)

    # Mostrar la figura
    plt.show()

    # Mostrar información
    mostrar_info_geometrica()
    exportar_coordenadas_tikz()

    # Opción para guardar
    respuesta = input("\n💾 ¿Deseas guardar la figura? (s/n): ").lower()
    if respuesta in ['s', 'si', 'sí', 'y', 'yes']:
        guardar_figura()

    print("\n✨ ¡Proceso completado!")
    print("📚 Figura lista para uso académico y presentaciones.")
