import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon

# Crear figura
fig, ax = plt.subplots(figsize=(10, 8))

# === COORDENADAS EXACTAS DE LA IMAGEN ORIGINAL ===

# Trapecio OPQR (azul cian)
O = np.array([1, 1.5])
P = np.array([3.5, 1.5])  
Q = np.array([3, 3.5])
R = np.array([1.5, 3.5])

# Trapecio EFGH (morado)
E = np.array([6, 0.5])
F = np.array([4.5, 2])
G = np.array([5.5, 2.5])
H = np.array([7, 2.5])

# === LÍNEA K ===
x_k = np.linspace(0, 8, 100)
y_k = 0.6 * x_k + 0.8
ax.plot(x_k, y_k, 'k-', linewidth=2)

# === TRAPECIOS ===
# Azul cian
trapecio_azul = Polygon([O, P, Q, R], facecolor='#00CED1', alpha=0.8, edgecolor='black', linewidth=1.5)
ax.add_patch(trapecio_azul)

# Morado
trapecio_morado = Polygon([E, F, G, H], facecolor='#8B008B', alpha=0.8, edgecolor='black', linewidth=1.5)
ax.add_patch(trapecio_morado)

# === ETIQUETAS ===
ax.text(O[0]-0.15, O[1], 'O', fontsize=14, fontweight='bold', ha='right', va='center')
ax.text(P[0], P[1]-0.2, 'P', fontsize=14, fontweight='bold', ha='center', va='top')
ax.text(Q[0]+0.15, Q[1], 'Q', fontsize=14, fontweight='bold', ha='left', va='center')
ax.text(R[0], R[1]+0.15, 'R', fontsize=14, fontweight='bold', ha='center', va='bottom')

ax.text(E[0], E[1]-0.2, 'E', fontsize=14, fontweight='bold', ha='center', va='top')
ax.text(F[0]-0.15, F[1], 'F', fontsize=14, fontweight='bold', ha='right', va='center')
ax.text(G[0], G[1]+0.15, 'G', fontsize=14, fontweight='bold', ha='center', va='bottom')
ax.text(H[0]+0.15, H[1], 'H', fontsize=14, fontweight='bold', ha='left', va='center')

ax.text(7, 5, 'k', fontsize=16, fontweight='bold')

# === CONFIGURACIÓN ===
ax.set_xlim(0, 8)
ax.set_ylim(0, 5.5)
ax.set_aspect('equal')
ax.axis('off')

plt.tight_layout()
plt.show()
